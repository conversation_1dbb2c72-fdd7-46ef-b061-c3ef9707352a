<?php
#region region DOCS
/** @var Allocation $modallocation */
/** @var string $idallocation */
/** @var AllocationItem[] $listaAllocationItems */

use App\classes\Allocation;
use App\classes\AllocationItem;

#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Editar Asignación</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h1 class="page-header mb-0">Editar Asignación</h1>
            <a href="allocations" class="btn btn-default">
                <i class="fa fa-arrow-left me-1"></i> Regresar
            </a>
        </div>

        <hr>
        <!-- END page-header -->

        <!-- BEGIN tabs navigation -->
        <ul class="nav nav-tabs mb-3" id="allocationTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                    <i class="fa fa-edit me-1"></i> General
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="items-tab" data-bs-toggle="tab" data-bs-target="#items" type="button" role="tab" aria-controls="items" aria-selected="false">
                    <i class="fa fa-list me-1"></i> Items de Asignación
                </button>
            </li>
        </ul>
        <!-- END tabs navigation -->

        <!-- BEGIN tab content -->
        <div class="tab-content" id="allocationTabContent">
            <!-- BEGIN General tab -->
            <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                <?php #region region FORM ?>
        <form id="allocationForm">
            <input type="hidden" id="idallocation" name="idallocation" value="<?php echo @recover_var($idallocation) ?>">

            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <label class="form-label">Nombre:</label>
                    <input type="text" name="nombre" id="nombre" value="<?php echo @recover_var($modallocation->getNombre()) ?>" class="form-control" onclick="this.focus();this.select('')" autofocus/>
                </div>
                <!-- END text -->
                <!-- BEGIN porcentaje -->
                <div class="col-md-6 col-xs-12">
                    <label class="form-label">Porcentaje:</label>
                    <input type="number" name="porcentaje" id="porcentaje" value="<?php echo @recover_var($modallocation->getPorcentaje()) ?>" class="form-control" step="0.1" min="0" max="100" onclick="this.focus();this.select('')"/>
                </div>
                <!-- END porcentaje -->
            </div>
            <!-- END row -->

            <!-- BEGIN row -->
            <div class="row mt-3">
                <div class="col-md-12 col-xs-12">
                    <button type="submit" id="sub_mod_allocation" name="sub_mod_allocation" class="btn btn-success w-100">
                        <i class="fa fa-save me-1"></i> Actualizar Asignación
                    </button>
                </div>
            </div>
            <!-- END row -->
        </form>
        <?php #endregion form ?>
            </div>
            <!-- END General tab -->

            <!-- BEGIN Items tab -->
            <div class="tab-pane fade" id="items" role="tabpanel" aria-labelledby="items-tab">
                <!-- BEGIN AllocationItems Section -->

        <?php #region region FORM AllocationItem ?>
        <form id="addAllocationItemForm" class="mb-4">
            <input type="hidden" name="idallocation" value="<?php echo @recover_var($idallocation) ?>">

            <div class="row">
                <div class="col-md-3 col-xs-12 mb-3">
                    <label class="form-label" for="item_nombre">Nombre:</label>
                    <input type="text" name="item_nombre" id="item_nombre" class="form-control" required placeholder="Ej: Inversión A">
                </div>
                <div class="col-md-3 col-xs-12 mb-3">
                    <label class="form-label" for="item_porcentaje">Porcentaje:</label>
                    <input type="number" name="item_porcentaje" id="item_porcentaje" class="form-control" required step="0.1" min="0" max="100" placeholder="Ej: 25.5">
                </div>
                <div class="col-md-3 col-xs-12 mb-3">
                    <label class="form-label" for="item_valor">Valor:</label>
                    <input type="text" name="item_valor" id="item_valor" class="form-control" required placeholder="Ej: 500.000" data-type="currencysinsigno">
                </div>
                <div class="col-md-3 col-xs-12 mb-3 d-flex align-items-end">
                    <button type="submit" name="sub_add_allocation_item" class="btn btn-primary w-100">
                        <i class="fa fa-plus me-1"></i> Agregar Item
                    </button>
                </div>
            </div>
        </form>
        <?php #endregion form AllocationItem ?>

        <?php #region region PANEL AllocationItems ?>
        <div class="panel panel-inverse mt-3" id="allocationItemsPanel">
            <div class="panel-heading">
                <h4 class="panel-title d-flex justify-content-between align-items-center">
                    <span>Items de Asignación:</span>
                    <div>
                        <span class="badge bg-info ms-2 fs-12px">Total: <?php echo $totalAllocationItems; ?> items</span>
                        <span class="badge bg-warning ms-2 fs-12px">
                            Porcentaje: <?php echo number_format($sumaPorcentajes, 1); ?>%
                        </span>
                        <span class="badge bg-success ms-2 fs-12px">
                            Valor: <?php echo format_currency($sumaValores); ?>
                        </span>
                    </div>
                </h4>
            </div>
            <!-- BEGIN panel-body -->
            <div class="table-nowrap bg-gray-900" style="overflow: auto" id="allocationItemsTableContainer">
                <?php #region region TABLE AllocationItems ?>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th class="w-50px">Acciones</th>
                            <th class="text-center">Nombre</th>
                            <th class="text-center">%</th>
                            <th class="text-center">% recomendado</th>
                            <th class="text-center">Bolsillo</th>
                            <th class="text-center">Valor</th>
                        </tr>
                    </thead>
                    <tbody class="fs-13px" id="allocationItemsTableBody">
                        <?php
                        if (!empty($listaAllocationItems)):
                            $totalItems = count($listaAllocationItems);
                            $recommendedPercentage = $totalItems > 0 ? (100 / $totalItems) : 0;
                        ?>
                            <?php foreach ($listaAllocationItems as $item): ?>
                                <tr>
                                    <td class="align-middle text-center">
                                        <i class="fa fa-trash fa-md cursor-pointer text-danger"
                                           data-bs-toggle="modal"
                                           data-bs-target="#mdl_del_allocation_item"
                                           data-idallocationitem="<?php echo $item->getId(); ?>"
                                           title="Eliminar item"></i>
                                    </td>
                                    <td class="align-middle"><?php echo htmlspecialchars($item->getNombre() ?? ''); ?></td>
                                    <td class="text-center align-middle">
                                        <span class="percentage-editable cursor-pointer"
                                              data-id="<?php echo $item->getId(); ?>"
                                              title="Doble clic para editar">
                                            <?php echo number_format($item->getPorcentaje(), 1); ?>%
                                        </span>
                                    </td>
                                    <td class="text-center align-middle text-dark"><?php echo number_format($recommendedPercentage, 2); ?>%</td>
                                    <td class="text-end align-middle">
                                        <?php echo format_currency($item->getValorBolsillo() ?? 0); ?>
                                    </td>
                                    <td class="text-end align-middle">
                                        <span class="valor-editable cursor-pointer"
                                              data-id="<?php echo $item->getId(); ?>"
                                              data-valor="<?php echo $item->getValor(); ?>"
                                              title="Doble clic para editar">
                                            <?php echo format_currency($item->getValor()); ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center text-muted">No hay items de asignación registrados.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr class="fw-bold">
                            <td class="align-middle"></td>
                            <td class="text-end align-middle">Total:</td>
                            <td class="text-center align-middle">
                                <span class="<?php echo ($sumaPorcentajes == 100.0) ? 'text-success' : 'text-warning'; ?>">
                                    <?php echo number_format($sumaPorcentajes, 1); ?>%
                                </span>
                            </td>
                            <td class="text-center align-middle">
                                <span class="text-info">100.00%</span>
                            </td>
                            <td class="text-end align-middle">
                                <span class="text-success"><?php echo format_currency($sumaValorBolsillo); ?></span>
                            </td>
                            <td class="text-end align-middle pe-3">
                                <span class="text-success"><?php echo format_currency($sumaValores); ?></span>
                            </td>
                        </tr>
                    </tfoot>
                </table>
                <?php #endregion table AllocationItems ?>
            </div>
            <!-- END panel-body -->
        </div>
        <?php #endregion panel AllocationItems ?>
        <!-- END AllocationItems Section -->
            </div>
            <!-- END Items tab -->
        </div>
        <!-- END tab content -->

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region MODAL mdl_del_allocation_item ?>
<div class="modal fade" id="mdl_del_allocation_item">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="deleteAllocationItemForm">
                <input type="hidden" name="idallocation" value="<?php echo @recover_var($idallocation) ?>">
                <input type="hidden" id="mdl_del_allocation_item_id" name="idallocationitem">

                <div class="modal-header">
                    <h4 class="modal-title">Eliminar Item de Asignación</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <p>¿Está seguro que desea eliminar este item de asignación?</p>
                    <p class="text-muted small">Esta acción no se puede deshacer.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fa fa-times me-1"></i> Cancelar
                    </button>
                    <button type="submit" name="sub_del_allocation_item" class="btn btn-danger">
                        <i class="fa fa-trash me-1"></i> Eliminar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php #endregion mdl_del_allocation_item ?>

<?php #region region CSS ?>
<style>
/* Simple Tab Styling */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    color: #6c757d;
    font-weight: 500;
    padding: 10px 16px;
    border: 1px solid transparent;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #495057;
}

.nav-tabs .nav-link.active {
    color: #0f766e;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    font-weight: 600;
}

.tab-content {
    background: #fff;
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 20px;
    border-radius: 0 0 6px 6px;
}

/* Dark mode adjustments */
.dark-mode .nav-tabs {
    border-bottom-color: #495057;
}

.dark-mode .nav-tabs .nav-link {
    color: #adb5bd;
}

.dark-mode .nav-tabs .nav-link:hover {
    border-color: #495057 #495057 #495057;
    color: #f8f9fa;
}

.dark-mode .nav-tabs .nav-link.active {
    background-color: #2d353c;
    border-color: #495057 #495057 #2d3748;
}

.dark-mode .tab-content {
    background: #2d353c;
    border-color: #495057;
    color: #e2e8f0;
}

/* Toast notification styles (same as epartido_probabilidades.view.php) */
.floating-badge-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    z-index: 9999;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    max-width: 350px;
    min-width: 200px;
    line-height: 1.4;
    word-wrap: break-word;
}

.floating-badge-toast.show {
    opacity: 1;
    transform: translateX(0);
}

.floating-badge-toast.hide {
    opacity: 0;
    transform: translateX(100%);
}

.floating-badge-toast.error {
    background-color: #dc3545;
}

/* Inline editing styles for percentage column */
.percentage-editable {
    border: 1px solid transparent;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.percentage-editable:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: #6c757d;
}

.dark-mode .percentage-editable:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: #495057;
}

/* Inline editing styles for valor column */
.valor-editable {
    border: 1px solid transparent;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.valor-editable:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: #6c757d;
}

.dark-mode .valor-editable:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: #495057;
}
</style>
<?php #endregion CSS ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<!-- Currency formatting script -->
<script src="resources/js/formatcurrency2.js"></script>

<script type="text/javascript">
    // Function to clean currency string to a numeric string for server
    function cleanCurrencyForServer(value) {
        if (value === null || value === undefined || String(value).trim() === '') return '';
        // Remove dots (thousand separators for es-CO)
        return String(value).replace(/\./g, '');
    }

    // Toast notification function (same as epartido_probabilidades.view.php)
    function showToastMessage(message, isError = false) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.classList.add('floating-badge-toast');
        if (isError) {
            toast.classList.add('error');
        }
        document.body.appendChild(toast);
        setTimeout(() => { toast.classList.add('show'); }, 10);
        setTimeout(() => {
            toast.classList.add('hide');
            setTimeout(() => { toast.remove(); }, 300);
        }, 3000);
    }

    // Function to refresh only the AllocationItems table section
    function refreshAllocationItemsTable() {
        const formData = new FormData();
        formData.append('action', 'get_allocation_items_data');
        formData.append('idallocation', document.getElementById('idallocation').value);

        fetch('editar-allocation', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                updateAllocationItemsTable(data.data);
            } else {
                console.error('Error refreshing table:', data.message);
                location.reload(); // Fallback to full page reload
            }
        })
        .catch(error => {
            console.error('Error refreshing table:', error);
            location.reload(); // Fallback to full page reload
        });
    }

    // Function to update the AllocationItems table with new data
    function updateAllocationItemsTable(data) {
        // Update statistics in panel heading
        const panelTitle = document.querySelector('#allocationItemsPanel .panel-title');
        if (panelTitle) {
            const badgesHtml = `
                <span class="badge bg-info ms-2 fs-12px">Total: ${data.totalItems} items</span>
                <span class="badge bg-warning ms-2 fs-12px">Porcentaje: ${data.sumaPorcentajes.toFixed(1)}%</span>
                <span class="badge bg-success ms-2 fs-12px">Valor: ${formatCurrency(data.sumaValores)}</span>
            `;
            panelTitle.innerHTML = '<span>Items de Asignación:</span><div>' + badgesHtml + '</div>';
        }

        // Update table body
        const tableBody = document.getElementById('allocationItemsTableBody');
        if (tableBody) {
            if (data.items.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No hay items de asignación registrados.</td></tr>';
            } else {
                let rowsHtml = '';
                const totalItems = data.items.length;
                const recommendedPercentage = totalItems > 0 ? (100 / totalItems) : 0;

                data.items.forEach(item => {
                    rowsHtml += `
                        <tr>
                            <td class="align-middle text-center">
                                <i class="fa fa-trash fa-md cursor-pointer text-danger"
                                   data-bs-toggle="modal"
                                   data-bs-target="#mdl_del_allocation_item"
                                   data-idallocationitem="${item.id}"
                                   title="Eliminar item"></i>
                            </td>
                            <td class="align-middle">${escapeHtml(item.nombre)}</td>
                            <td class="text-center align-middle">
                                <span class="percentage-editable cursor-pointer"
                                      data-id="${item.id}"
                                      title="Doble clic para editar">
                                    ${item.porcentaje.toFixed(1)}%
                                </span>
                            </td>
                            <td class="text-center align-middle text-dark">${recommendedPercentage.toFixed(2)}%</td>
                            <td class="text-end align-middle">
                                ${formatCurrency(item.valor_bolsillo)}
                            </td>
                            <td class="text-end align-middle">
                                <span class="valor-editable cursor-pointer"
                                      data-id="${item.id}"
                                      data-valor="${item.valor}"
                                      title="Doble clic para editar">
                                    ${formatCurrency(item.valor)}
                                </span>
                            </td>
                        </tr>
                    `;
                });
                tableBody.innerHTML = rowsHtml;
            }
        }

        // Update table footer
        const tableFooter = document.querySelector('#allocationItemsTableContainer tfoot tr');
        if (tableFooter) {
            const percentageClass = (data.sumaPorcentajes === 100.0) ? 'text-success' : 'text-warning';
            tableFooter.innerHTML = `
                <td class="align-middle"></td>
                <td class="text-end align-middle">Total:</td>
                <td class="text-center align-middle">
                    <span class="${percentageClass}">${data.sumaPorcentajes.toFixed(1)}%</span>
                </td>
                <td class="text-center align-middle">
                    <span class="text-info">100.00%</span>
                </td>
                <td class="text-end align-middle">
                    <span class="text-success">${formatCurrency(data.sumaValorBolsillo)}</span>
                </td>
                <td class="text-end align-middle pe-3">
                    <span class="text-success">${formatCurrency(data.sumaValores)}</span>
                </td>
            `;
        }
    }

    // Helper function to format currency (Colombian peso format)
    function formatCurrency(value) {
        if (value === null || value === undefined || value === 0) return '$0';
        return '$' + Math.round(value).toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    }

    // Helper function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Function to update allocation item percentage via AJAX
    function updateAllocationItemPercentage(id, newPercentage) {
        const formData = new FormData();
        formData.append('action', 'update_allocation_item_percentage');
        formData.append('idallocationitem', id);
        formData.append('porcentaje', newPercentage);

        return fetch('editar-allocation', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Refresh the table to update totals
                refreshAllocationItemsTable();
                return data;
            } else {
                throw new Error(data.message || 'Error updating percentage');
            }
        });
    }

    // Function to update allocation item valor via AJAX
    function updateAllocationItemValor(id, newValor) {
        const formData = new FormData();
        formData.append('action', 'update_allocation_item_valor');
        formData.append('idallocationitem', id);
        formData.append('valor', newValor);

        return fetch('editar-allocation', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Refresh the table to update totals
                refreshAllocationItemsTable();
                return data;
            } else {
                throw new Error(data.message || 'Error updating valor');
            }
        });
    }

    // Function to attach inline editing functionality to percentage cells
    function attachInlineEditPercentage(container) {
        container.addEventListener('dblclick', function(e) {
            const span = e.target.closest('.percentage-editable');
            if (!span) return;
            if (span.dataset.editing === '1') return;

            span.dataset.editing = '1';
            const id = span.getAttribute('data-id');
            const text = span.textContent.trim();
            const currentValue = parseFloat(text.replace('%', ''));

            const input = document.createElement('input');
            input.type = 'number';
            input.className = 'form-control form-control-sm';
            input.style.width = '80px';
            input.style.textAlign = 'center';
            input.value = currentValue;
            input.min = '0';
            input.max = '100';
            input.step = '0.1';

            span.replaceWith(input);
            input.focus();
            input.select();

            let replaced = false; // Flag to prevent double replacement

            const cancelToSpan = () => {
                if (replaced) return; // Prevent double replacement
                replaced = true;
                const newSpan = document.createElement('span');
                newSpan.className = 'percentage-editable cursor-pointer';
                newSpan.setAttribute('data-id', id);
                newSpan.setAttribute('title', 'Doble clic para editar');
                newSpan.textContent = text;
                input.replaceWith(newSpan);
            };

            const commit = () => {
                if (replaced) return; // Prevent double replacement
                const newValue = parseFloat(input.value);

                // Validate input
                if (isNaN(newValue) || newValue < 0 || newValue > 100) {
                    showToastMessage('El porcentaje debe estar entre 0 y 100', true);
                    cancelToSpan();
                    return;
                }

                if (newValue === currentValue) {
                    cancelToSpan();
                    return;
                }

                updateAllocationItemPercentage(id, newValue).then(data => {
                    if (replaced) return; // Prevent double replacement
                    replaced = true;
                    const newSpan = document.createElement('span');
                    newSpan.className = 'percentage-editable cursor-pointer';
                    newSpan.setAttribute('data-id', id);
                    newSpan.setAttribute('title', 'Doble clic para editar');
                    newSpan.textContent = newValue.toFixed(1) + '%';
                    input.replaceWith(newSpan);
                    showToastMessage('Porcentaje actualizado correctamente');
                }).catch(error => {
                    showToastMessage(error.message || 'Error al actualizar porcentaje', true);
                    cancelToSpan();
                });
            };

            input.addEventListener('keydown', function(ev) {
                if (ev.key === 'Enter') {
                    ev.preventDefault();
                    commit();
                }
                if (ev.key === 'Escape') {
                    ev.preventDefault();
                    cancelToSpan();
                }
            });

            input.addEventListener('blur', cancelToSpan);
        });
    }

    // Function to attach inline editing functionality to valor cells
    function attachInlineEditValor(container) {
        container.addEventListener('dblclick', function(e) {
            const span = e.target.closest('.valor-editable');
            if (!span) return;
            if (span.dataset.editing === '1') return;

            span.dataset.editing = '1';
            const id = span.getAttribute('data-id');
            const currentValor = parseFloat(span.getAttribute('data-valor')) || 0;
            const text = span.textContent.trim();

            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'form-control form-control-sm';
            input.style.width = '120px';
            input.style.textAlign = 'right';
            input.value = Math.round(currentValor).toString();
            input.setAttribute('data-type', 'currencysinsigno');

            span.replaceWith(input);

            // Apply currency formatting to the input
            if (typeof formatCurrency2 !== 'undefined') {
                formatCurrency2();
            }

            input.focus();
            input.select();

            let replaced = false; // Flag to prevent double replacement

            const cancelToSpan = () => {
                if (replaced) return; // Prevent double replacement
                replaced = true;
                const newSpan = document.createElement('span');
                newSpan.className = 'valor-editable cursor-pointer';
                newSpan.setAttribute('data-id', id);
                newSpan.setAttribute('data-valor', currentValor);
                newSpan.setAttribute('title', 'Doble clic para editar');
                newSpan.textContent = text;
                input.replaceWith(newSpan);
            };

            const commit = () => {
                if (replaced) return; // Prevent double replacement
                const cleanedValue = cleanCurrencyForServer(input.value);
                const newValue = parseFloat(cleanedValue) || 0;

                if (newValue === currentValor) {
                    cancelToSpan();
                    return;
                }

                updateAllocationItemValor(id, newValue).then(data => {
                    if (replaced) return; // Prevent double replacement
                    replaced = true;
                    const newSpan = document.createElement('span');
                    newSpan.className = 'valor-editable cursor-pointer';
                    newSpan.setAttribute('data-id', id);
                    newSpan.setAttribute('data-valor', newValue);
                    newSpan.setAttribute('title', 'Doble clic para editar');
                    newSpan.textContent = formatCurrency(newValue);
                    input.replaceWith(newSpan);
                    showToastMessage('Valor actualizado correctamente');
                }).catch(error => {
                    showToastMessage(error.message || 'Error al actualizar valor', true);
                    cancelToSpan();
                });
            };

            input.addEventListener('keydown', function(ev) {
                if (ev.key === 'Enter') {
                    ev.preventDefault();
                    commit();
                }
                if (ev.key === 'Escape') {
                    ev.preventDefault();
                    cancelToSpan();
                }
            });

            input.addEventListener('blur', cancelToSpan);
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Handle allocation form submission
        const allocationForm = document.getElementById('allocationForm');
        if (allocationForm) {
            allocationForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                formData.append('ajax_check', '1');
                formData.append('sub_mod_allocation', '1');

                fetch('editar-allocation', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToastMessage(data.message);
                    } else {
                        showToastMessage(data.message, true);
                    }
                })
                .catch(error => {
                    showToastMessage('Error al procesar la solicitud', true);
                });
            });
        }

        // Handle allocation item form submission
        const addAllocationItemForm = document.getElementById('addAllocationItemForm');
        const itemValorInput = document.getElementById('item_valor');

        if (addAllocationItemForm && itemValorInput) {
            // Function to submit the form
            function submitAllocationItemForm() {
                // Clean currency format before submitting
                const originalValue = itemValorInput.value;
                itemValorInput.value = cleanCurrencyForServer(originalValue);

                const formData = new FormData(addAllocationItemForm);
                formData.append('ajax_check', '1');
                formData.append('sub_add_allocation_item', '1');

                fetch('editar-allocation', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToastMessage(data.message);
                        // Clear form and refresh only the items table section
                        addAllocationItemForm.reset();
                        refreshAllocationItemsTable();
                    } else {
                        showToastMessage(data.message, true);
                        // Restore original value if error
                        itemValorInput.value = originalValue;
                    }
                })
                .catch(error => {
                    showToastMessage('Error al procesar la solicitud', true);
                    // Restore original value if error
                    itemValorInput.value = originalValue;
                });
            }

            // Handle form submission via button click
            addAllocationItemForm.addEventListener('submit', function(e) {
                e.preventDefault();
                submitAllocationItemForm();
            });

            // Handle Enter key submission on valor field
            itemValorInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    submitAllocationItemForm();
                }
            });
        }

        // Handle delete allocation item form submission
        const deleteAllocationItemForm = document.getElementById('deleteAllocationItemForm');
        if (deleteAllocationItemForm) {
            deleteAllocationItemForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                formData.append('ajax_check', '1');
                formData.append('sub_del_allocation_item', '1');

                fetch('editar-allocation', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToastMessage(data.message);
                        // Close modal and refresh only the items table section
                        const modal = bootstrap.Modal.getInstance(document.getElementById('mdl_del_allocation_item'));
                        modal.hide();
                        // Instead of full page reload, refresh just the items tab content
                        refreshAllocationItemsTable();
                    } else {
                        showToastMessage(data.message, true);
                    }
                })
                .catch(error => {
                    showToastMessage('Error al procesar la solicitud', true);
                });
            });
        }

        // Initialize inline editing for percentage cells
        const allocationItemsTableContainer = document.getElementById('allocationItemsTableContainer');
        if (allocationItemsTableContainer) {
            attachInlineEditPercentage(allocationItemsTableContainer);
            attachInlineEditValor(allocationItemsTableContainer);
        }
    });
</script>

<?php #region region JS mdl_del_allocation_item ?>
<script type="text/javascript">
    $('#mdl_del_allocation_item').on('shown.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const recipient_idallocationitem = button.data('idallocationitem');

        const mdl_del_allocation_item_id = document.getElementById('mdl_del_allocation_item_id');
        mdl_del_allocation_item_id.value = recipient_idallocationitem;
    });
</script>
<?php #endregion js mdl_del_allocation_item ?>
<?php #endregion js ?>

</body>
</html>
